/**
 * Mobile Navigation Enhancements
 * Improves navigation experience on mobile devices
 */

/* ===== MOBILE NAVIGATION BASE STYLES ===== */

/* Enhanced glass navbar for mobile */
@media (max-width: 767px) {
  .glass-navbar {
    /* Improved backdrop blur */
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);

    /* Better background with transparency */
    background: rgba(8, 8, 8, 0.85) !important;

    /* Enhanced border - only top border for bottom-stuck design */
    border: none !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;

    /* Improved shadow - reduced for bottom-stuck design */
    box-shadow:
      0 -4px 16px rgba(0, 0, 0, 0.2),
      0 0 0 1px rgba(255, 255, 255, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);

    /* Safe area padding for devices with home indicator */
    padding-bottom: max(12px, calc(12px + env(safe-area-inset-bottom))) !important;

    /* Prevent interaction with content behind */
    pointer-events: auto;

    /* Bottom-stuck positioning */
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    border-radius: 0 !important; /* Remove all rounded corners */
    transform: none !important;
    justify-content: space-evenly !important;

    /* Ensure horizontal layout for all mobile devices including iPhone 14 Pro Max */
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    flex-wrap: nowrap !important;
  }
  
  /* Navigation button enhancements */
  .glass-navbar .chakra-button {
    /* Better touch targets */
    min-height: 48px !important;
    min-width: 48px !important;
    
    /* Improved visual feedback */
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
    
    /* Better hover states for touch */
    -webkit-tap-highlight-color: rgba(0, 204, 133, 0.2);
    
    /* Prevent text selection */
    -webkit-user-select: none;
    user-select: none;
  }
  
  /* Active navigation indicator */
  .nav-active-indicator {
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background: #00CC85;
    border-radius: 50%;
    box-shadow: 0 0 8px rgba(0, 204, 133, 0.6);
  }
}

/* ===== IPHONE 14 PRO MAX SPECIFIC HORIZONTAL NAVIGATION ===== */
@media (min-width: 428px) and (max-width: 767px) {
  .glass-navbar {
    /* Force horizontal layout for iPhone 14 Pro Max and similar devices */
    display: flex !important;
    flex-direction: row !important;
    justify-content: center !important;
    align-items: center !important;
    flex-wrap: nowrap !important;
    gap: 16px !important;
    padding: 16px 20px !important;
    padding-bottom: max(16px, calc(16px + env(safe-area-inset-bottom))) !important;
  }

  .glass-navbar .chakra-button {
    flex: 0 0 auto !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }
}

/* ===== DESKTOP/TABLET NAVIGATION (768px+) - LEFT SIDE VERTICAL ===== */
@media (min-width: 768px) {
  .glass-navbar {
    /* Desktop styling */
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    background: rgba(8, 8, 8, 0.85) !important;

    /* Desktop border and shadow */
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      0 0 0 1px rgba(255, 255, 255, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);

    /* Left-side vertical positioning */
    left: 16px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    bottom: auto !important;
    right: auto !important;
    width: auto !important;
    border-radius: 12px !important; /* Rounded corners for desktop */
    justify-content: flex-start !important;
    flex-direction: column !important;

    /* Desktop padding */
    padding: 16px 12px !important;
    gap: 12px !important;
  }

  .glass-navbar .chakra-button {
    min-height: 48px !important;
    min-width: 48px !important;
  }

  /* Desktop active indicator - left side */
  .nav-active-indicator {
    position: absolute;
    left: -2px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 4px;
    background: #00CC85;
    border-radius: 50%;
  }
}

/* ===== MOBILE NAVIGATION RESPONSIVENESS ===== */

/* Very small screens (< 320px) */
@media (max-width: 319px) {
  .glass-navbar {
    gap: 4px !important;
    padding: 6px 4px !important;
    padding-bottom: max(6px, calc(6px + env(safe-area-inset-bottom))) !important;
    border-radius: 0 !important; /* Remove rounded corners */
  }

  .glass-navbar .chakra-button {
    min-height: 36px !important;
    min-width: 36px !important;
    flex: 1 !important;
    flex-shrink: 0 !important;
    max-width: 50px !important;
  }
}

/* Small mobile screens (320px - 374px) */
@media (min-width: 320px) and (max-width: 374px) {
  .glass-navbar {
    gap: 6px !important;
    padding: 10px 6px !important;
    padding-bottom: max(10px, calc(10px + env(safe-area-inset-bottom))) !important;
    border-radius: 0 !important; /* Remove rounded corners */
  }

  .glass-navbar .chakra-button {
    min-height: 40px !important;
    min-width: 40px !important;
    flex: 1 !important;
    flex-shrink: 0 !important;
    max-width: 55px !important;
  }
}

/* Standard mobile screens (375px - 413px) */
@media (min-width: 375px) and (max-width: 413px) {
  .glass-navbar {
    gap: 10px !important;
    padding: 12px 10px !important;
    padding-bottom: max(12px, calc(12px + env(safe-area-inset-bottom))) !important;
    border-radius: 0 !important; /* Remove rounded corners */
  }

  .glass-navbar .chakra-button {
    min-height: 48px !important;
    min-width: 48px !important;
    flex: 1 !important;
    flex-shrink: 0 !important;
    max-width: 65px !important;
  }
}

/* Large mobile screens (414px - 767px) */
@media (min-width: 414px) and (max-width: 767px) {
  .glass-navbar {
    gap: 14px !important;
    padding: 14px 14px !important;
    padding-bottom: max(14px, calc(14px + env(safe-area-inset-bottom))) !important;
    border-radius: 0 !important; /* Remove rounded corners */
  }

  .glass-navbar .chakra-button {
    min-height: 52px !important;
    min-width: 52px !important;
    flex: 1 !important;
    flex-shrink: 0 !important;
    max-width: 75px !important;
  }
}

/* ===== MOBILE NAVIGATION ACCESSIBILITY ===== */

@media (max-width: 767px) {
  /* Improve focus indicators */
  .glass-navbar .chakra-button:focus {
    outline: 2px solid #00CC85;
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(0, 204, 133, 0.2);
  }
  
  /* Better active states */
  .glass-navbar .chakra-button:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }
  
  /* Improve contrast for better visibility */
  .glass-navbar .chakra-button[aria-current="page"] {
    background: rgba(0, 204, 133, 0.15) !important;
    border: 1px solid rgba(0, 204, 133, 0.3);
  }
}

/* ===== MOBILE LOGO ENHANCEMENTS ===== */

@media (max-width: 767px) {
  .mobile-logo-scroll {
    /* Better positioning for different screen sizes */
    top: max(12px, env(safe-area-inset-top)) !important;
    
    /* Improved backdrop */
    backdrop-filter: blur(12px) saturate(180%);
    -webkit-backdrop-filter: blur(12px) saturate(180%);
    
    /* Better background */
    background: rgba(8, 8, 8, 0.7);
    border-radius: 50%;
    padding: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  /* Logo image optimizations */
  .mobile-logo-scroll img {
    width: 32px !important;
    height: 32px !important;
    border-radius: 50%;
  }
}

/* ===== MOBILE NAVIGATION ANIMATIONS ===== */

@media (max-width: 767px) {
  /* Smooth slide-up animation for navbar */
  .glass-navbar {
    animation: slideUpNavbar 0.3s ease-out;
  }
  
  @keyframes slideUpNavbar {
    from {
      transform: translateX(-50%) translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateX(-50%) translateY(0);
      opacity: 1;
    }
  }
  
  /* Logo fade animation */
  .mobile-logo-scroll {
    animation: fadeInLogo 0.4s ease-out;
  }
  
  @keyframes fadeInLogo {
    from {
      transform: translateX(-50%) translateY(-20px);
      opacity: 0;
    }
    to {
      transform: translateX(-50%) translateY(0);
      opacity: 1;
    }
  }
  
  /* Reduce animations for users who prefer reduced motion */
  @media (prefers-reduced-motion: reduce) {
    .glass-navbar,
    .mobile-logo-scroll {
      animation: none !important;
    }
    
    .glass-navbar .chakra-button {
      transition: none !important;
    }
  }
}

/* ===== MOBILE NAVIGATION LANDSCAPE MODE ===== */

@media (max-width: 767px) and (orientation: landscape) {
  /* Optimize for landscape orientation */
  .mobile-logo-scroll {
    top: 8px !important;
    padding: 6px !important;
  }
  
  .mobile-logo-scroll img {
    width: 28px !important;
    height: 28px !important;
  }
  
  .glass-navbar {
    bottom: 8px !important;
    padding: 8px 16px !important;
    padding-bottom: max(8px, calc(8px + env(safe-area-inset-bottom))) !important;
  }
  
  .glass-navbar .chakra-button {
    min-height: 44px !important;
    min-width: 44px !important;
  }
}

/* ===== MOBILE NAVIGATION PERFORMANCE ===== */

@media (max-width: 767px) {
  /* GPU acceleration for smooth performance */
  .glass-navbar,
  .mobile-logo-scroll {
    will-change: transform, opacity;
    transform: translateZ(0);
    backface-visibility: hidden;
  }
  
  /* Optimize button interactions */
  .glass-navbar .chakra-button {
    will-change: transform, background-color;
    contain: layout style paint;
  }
  
  /* Optimize icon rendering */
  .glass-navbar svg {
    will-change: auto;
    transform: translateZ(0);
  }
}

/* ===== MOBILE NAVIGATION STATES ===== */

@media (max-width: 767px) {
  /* Loading state */
  .glass-navbar.loading {
    opacity: 0.7;
    pointer-events: none;
  }
  
  /* Hidden state for scroll */
  .glass-navbar.hidden {
    transform: translateX(-50%) translateY(100%);
    opacity: 0;
    transition: all 0.3s ease-in-out;
  }
  
  /* Visible state */
  .glass-navbar.visible {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
    transition: all 0.3s ease-in-out;
  }
}
